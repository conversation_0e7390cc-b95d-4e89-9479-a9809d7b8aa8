"use client";

import { ReactNode, useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth';
import AccessRestrictedAlert from './AccessRestrictedAlert';

interface SimpleAccessCheckProps {
  database: string;
  children: ReactNode;
}

/**
 * 动态权限检查组件
 * 从数据库配置中读取权限要求，而不是硬编码
 */
export default function SimpleAccessCheck({ database, children }: SimpleAccessCheckProps) {
  const { user } = useAuth();
  const [accessLevel, setAccessLevel] = useState<string>('free'); // 默认为free，避免阻塞
  const [loading, setLoading] = useState(false); // 默认不加载，直接显示内容

  // 简化的权限检查 - 对于us_pmn直接允许访问
  useEffect(() => {
    // 对于已知的免费数据库，直接设置为free
    if (database === 'us_pmn' || database === 'us_class') {
      setAccessLevel('free');
      setLoading(false);
      console.log(`[SimpleAccessCheck] 已知免费数据库: ${database}`);
      return;
    }

    // 对于其他数据库，快速检查
    const fetchAccessLevel = async () => {
      try {
        setLoading(true);
        console.log(`[SimpleAccessCheck] 检查数据库权限: ${database}`);

        const response = await fetch('/api/config/databases');

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data[database]) {
            const dbConfig = result.data[database];
            const level = dbConfig.accessLevel || 'free';
            setAccessLevel(level);
            console.log(`[SimpleAccessCheck] 数据库 ${database} 访问级别: ${level}`);
          } else {
            // 数据库不存在，默认允许访问
            setAccessLevel('free');
            console.log(`[SimpleAccessCheck] 数据库 ${database} 不存在，默认允许访问`);
          }
        } else {
          // API失败，默认允许访问
          setAccessLevel('free');
          console.log(`[SimpleAccessCheck] API失败，默认允许访问`);
        }
      } catch (error) {
        // 错误时默认允许访问
        setAccessLevel('free');
        console.log(`[SimpleAccessCheck] 权限检查失败，默认允许访问:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchAccessLevel();
  }, [database]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">
          Checking permissions for {database}...
          {process.env.NODE_ENV === 'development' && (
            <div className="text-xs mt-2">
              If this takes too long, there might be an API issue.
            </div>
          )}
        </div>
      </div>
    );
  }

  // 免费数据库：所有人都可以访问（包括未登录用户）
  if (accessLevel === 'free') {
    if (process.env.NODE_ENV === 'development') {

    }
    return <>{children}</>;
  }

  // 需要高级权限的数据库
  if (accessLevel ==="premium" || accessLevel === 'enterprise') {
    // 未登录用户
    if (!user) {
      return (
        <AccessRestrictedAlert
          databaseCode={database}
          requiredLevel={accessLevel as 'free' | 'premium' | 'enterprise'}
        />
      );
    }

    // 检查会员级别
    const userLevel = user.membershipType;
    const hasAccess =
      (accessLevel ==="premium" && (userLevel ==="premium" || userLevel === 'enterprise')) ||
      (accessLevel ==="enterprise" && userLevel === 'enterprise');

    if (hasAccess) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ [Dynamic Permission] User has ${userLevel} access for ${database} (requires ${accessLevel})`);
      }
      return <>{children}</>;
    }

    // 权限不足
    return (
      <AccessRestrictedAlert
        databaseCode={database}
        requiredLevel={accessLevel as 'free' | 'premium' | 'enterprise'}
      />
    );
  }

  // 默认情况：允许访问（对于未知访问级别）
  if (process.env.NODE_ENV === 'development') {

  }
  return <>{children}</>;
}